version: '3.8'

services:
  emqx:
    image: emqx/emqx:5.1.0
    container_name: emqx-mqtt-server
    ports:
      - "1883:1883"      # MQTT TCP端口
      - "8083:8083"      # MQTT WebSocket端口
      - "8084:8084"      # MQTT SSL端口
      - "8883:8883"      # MQTT SSL端口
      - "18083:18083"    # Dashboard端口
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=127.0.0.1
      - EMQX_DASHBOARD__DEFAULT_USERNAME=admin
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=public
      - EMQX_AUTH__MNESIA__PASSWORD_HASH=sha256
    volumes:
      - emqx-data:/opt/emqx/data
      - emqx-log:/opt/emqx/log
    restart: unless-stopped
    networks:
      - mqtt-network

volumes:
  emqx-data:
  emqx-log:

networks:
  mqtt-network:
    driver: bridge
