#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物联网设备模拟器
模拟设备登录、数据上传、状态更新等功能
"""

import json
import time
import random
import threading
import paho.mqtt.client as mqtt
from datetime import datetime
import argparse
import sys

class IoTDeviceSimulator:
    def __init__(self, device_id, device_name, username, password, 
                 mqtt_host="localhost", mqtt_port=1883):
        self.device_id = device_id
        self.device_name = device_name
        self.username = username
        self.password = password
        self.mqtt_host = mqtt_host
        self.mqtt_port = mqtt_port
        
        # MQTT客户端
        self.client = mqtt.Client(client_id=f"device_{device_id}")
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        
        # 设备状态
        self.is_connected = False
        self.is_logged_in = False
        self.running = False
        
        # 传感器数据模拟
        self.temperature = 25.0
        self.humidity = 60.0
        self.pressure = 1013.25
        
    def on_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            print(f"[{self.device_id}] 已连接到MQTT服务器")
            self.is_connected = True
            
            # 订阅响应主题
            response_topic = f"device/{self.device_id}/login/response"
            client.subscribe(response_topic, qos=1)
            print(f"[{self.device_id}] 已订阅主题: {response_topic}")
            
            # 订阅指令主题
            command_topic = f"device/{self.device_id}/command"
            client.subscribe(command_topic, qos=1)
            print(f"[{self.device_id}] 已订阅主题: {command_topic}")
            
            # 发送登录请求
            self.send_login_request()
        else:
            print(f"[{self.device_id}] 连接失败，错误码: {rc}")
            
    def on_message(self, client, userdata, msg):
        """MQTT消息接收回调"""
        topic = msg.topic
        payload = msg.payload.decode('utf-8')
        print(f"[{self.device_id}] 收到消息 - 主题: {topic}, 内容: {payload}")
        
        try:
            if "/login/response" in topic:
                self.handle_login_response(payload)
            elif "/command" in topic:
                self.handle_command(payload)
        except Exception as e:
            print(f"[{self.device_id}] 处理消息失败: {e}")
            
    def on_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        print(f"[{self.device_id}] 已断开连接")
        self.is_connected = False
        self.is_logged_in = False
        
    def send_login_request(self):
        """发送登录请求"""
        login_topic = f"device/{self.device_id}/login"
        login_data = {
            "username": self.username,
            "password": self.password,
            "ip": "*************",  # 模拟IP地址
            "timestamp": int(time.time() * 1000)
        }
        
        payload = json.dumps(login_data, ensure_ascii=False)
        self.client.publish(login_topic, payload, qos=1)
        print(f"[{self.device_id}] 发送登录请求: {payload}")
        
    def handle_login_response(self, payload):
        """处理登录响应"""
        try:
            response = json.loads(payload)
            if response.get("success"):
                print(f"[{self.device_id}] 登录成功: {response.get('message')}")
                self.is_logged_in = True
                # 发送在线状态
                self.send_status("online")
            else:
                print(f"[{self.device_id}] 登录失败: {response.get('message')}")
                self.is_logged_in = False
        except Exception as e:
            print(f"[{self.device_id}] 解析登录响应失败: {e}")
            
    def handle_command(self, payload):
        """处理服务器指令"""
        try:
            command = json.loads(payload)
            cmd_type = command.get("type", "unknown")
            cmd_data = command.get("data", {})
            
            print(f"[{self.device_id}] 收到指令: {cmd_type}")
            
            if cmd_type == "get_status":
                self.send_device_info()
            elif cmd_type == "restart":
                print(f"[{self.device_id}] 执行重启指令")
                self.send_status("restarting")
                time.sleep(2)
                self.send_status("online")
            elif cmd_type == "update_interval":
                interval = cmd_data.get("interval", 30)
                print(f"[{self.device_id}] 更新数据上传间隔为: {interval}秒")
            else:
                print(f"[{self.device_id}] 未知指令: {cmd_type}")
                
        except Exception as e:
            print(f"[{self.device_id}] 处理指令失败: {e}")
            
    def send_status(self, status):
        """发送设备状态"""
        status_topic = f"device/{self.device_id}/status"
        status_data = {
            "status": status,
            "ip": "*************",
            "timestamp": int(time.time() * 1000)
        }
        
        payload = json.dumps(status_data, ensure_ascii=False)
        self.client.publish(status_topic, payload, qos=1)
        print(f"[{self.device_id}] 发送状态: {status}")
        
    def send_device_info(self):
        """发送设备信息"""
        data_topic = f"device/{self.device_id}/data"
        device_info = {
            "type": "device_info",
            "device_id": self.device_id,
            "device_name": self.device_name,
            "firmware_version": "1.0.0",
            "hardware_version": "2.1",
            "uptime": int(time.time()),
            "timestamp": int(time.time() * 1000)
        }
        
        payload = json.dumps(device_info, ensure_ascii=False)
        self.client.publish(data_topic, payload, qos=1)
        print(f"[{self.device_id}] 发送设备信息")
        
    def send_sensor_data(self):
        """发送传感器数据"""
        if not self.is_logged_in:
            return
            
        # 模拟传感器数据变化
        self.temperature += random.uniform(-2, 2)
        self.humidity += random.uniform(-5, 5)
        self.pressure += random.uniform(-1, 1)
        
        # 限制数据范围
        self.temperature = max(-40, min(80, self.temperature))
        self.humidity = max(0, min(100, self.humidity))
        self.pressure = max(900, min(1100, self.pressure))
        
        data_topic = f"device/{self.device_id}/data"
        sensor_data = {
            "type": "sensor_data",
            "temperature": round(self.temperature, 2),
            "humidity": round(self.humidity, 2),
            "pressure": round(self.pressure, 2),
            "timestamp": int(time.time() * 1000)
        }
        
        payload = json.dumps(sensor_data, ensure_ascii=False)
        self.client.publish(data_topic, payload, qos=1)
        print(f"[{self.device_id}] 发送传感器数据: 温度={sensor_data['temperature']}°C, "
              f"湿度={sensor_data['humidity']}%, 气压={sensor_data['pressure']}hPa")
        
    def data_upload_loop(self):
        """数据上传循环"""
        while self.running:
            if self.is_logged_in:
                self.send_sensor_data()
            time.sleep(30)  # 每30秒发送一次数据
            
    def start(self):
        """启动设备模拟器"""
        print(f"[{self.device_id}] 启动设备模拟器...")
        
        try:
            # 连接MQTT服务器
            self.client.connect(self.mqtt_host, self.mqtt_port, 60)
            self.client.loop_start()
            
            self.running = True
            
            # 启动数据上传线程
            data_thread = threading.Thread(target=self.data_upload_loop)
            data_thread.daemon = True
            data_thread.start()
            
            print(f"[{self.device_id}] 设备模拟器已启动，按Ctrl+C停止")
            
            # 主循环
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print(f"\n[{self.device_id}] 收到停止信号")
        except Exception as e:
            print(f"[{self.device_id}] 运行错误: {e}")
        finally:
            self.stop()
            
    def stop(self):
        """停止设备模拟器"""
        print(f"[{self.device_id}] 正在停止设备模拟器...")
        self.running = False
        
        if self.is_logged_in:
            self.send_status("offline")
            time.sleep(1)
            
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
            
        print(f"[{self.device_id}] 设备模拟器已停止")

def main():
    parser = argparse.ArgumentParser(description='物联网设备模拟器')
    parser.add_argument('--device-id', default='device001', help='设备ID')
    parser.add_argument('--device-name', default='温湿度传感器01', help='设备名称')
    parser.add_argument('--username', default='device001', help='用户名')
    parser.add_argument('--password', default='password123', help='密码')
    parser.add_argument('--mqtt-host', default='localhost', help='MQTT服务器地址')
    parser.add_argument('--mqtt-port', type=int, default=1883, help='MQTT服务器端口')
    
    args = parser.parse_args()
    
    # 创建并启动设备模拟器
    simulator = IoTDeviceSimulator(
        device_id=args.device_id,
        device_name=args.device_name,
        username=args.username,
        password=args.password,
        mqtt_host=args.mqtt_host,
        mqtt_port=args.mqtt_port
    )
    
    simulator.start()

if __name__ == "__main__":
    main()
