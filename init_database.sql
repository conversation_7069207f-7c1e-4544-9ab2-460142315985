-- 物联网传感器服务器数据库初始化脚本
-- 数据库: mqtt_server
-- 用户: root
-- 密码: 987654

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS mqtt_server CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mqtt_server;

-- 创建设备表
CREATE TABLE IF NOT EXISTS `devices` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `online` tinyint(1) DEFAULT '0',
  `last_ip_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_connected_at` datetime DEFAULT NULL,
  `last_disconnected_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `device_id` (`device_id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_username` (`username`),
  KEY `idx_online` (`online`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建MQTT消息表
CREATE TABLE IF NOT EXISTS `mqtt_messages` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `topic` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` text COLLATE utf8mb4_unicode_ci,
  `qos` int DEFAULT '0',
  `retained` tinyint(1) DEFAULT '0',
  `message_type` enum('INCOMING','OUTGOING') COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_message_type` (`message_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入测试设备数据
INSERT INTO `devices` (`device_id`, `device_name`, `username`, `password`, `online`) VALUES
('device001', '温湿度传感器01', 'device001', 'password123', 0),
('device002', '压力传感器01', 'device002', 'password456', 0),
('device003', '光照传感器01', 'device003', 'password789', 0)
ON DUPLICATE KEY UPDATE 
  `device_name` = VALUES(`device_name`),
  `password` = VALUES(`password`);

-- 显示创建结果
SELECT 'Database and tables created successfully!' as status;
SELECT COUNT(*) as device_count FROM devices;
SELECT * FROM devices;
