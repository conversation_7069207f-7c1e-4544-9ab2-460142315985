@echo off
chcp 65001 >nul
echo ========================================
echo 物联网传感器服务器API测试脚本
echo ========================================

set BASE_URL=http://localhost:8080

echo.
echo 正在测试API接口...
echo.

echo 1. 测试获取所有设备列表
curl -s -X GET "%BASE_URL%/api/devices" | echo.
echo.

echo 2. 测试获取在线设备列表
curl -s -X GET "%BASE_URL%/api/devices/online" | echo.
echo.

echo 3. 测试获取设备状态统计
curl -s -X GET "%BASE_URL%/api/devices/status/summary" | echo.
echo.

echo 4. 测试获取最新消息
curl -s -X GET "%BASE_URL%/api/mqtt/messages/latest?limit=5" | echo.
echo.

echo 5. 测试发送指令到设备001
curl -s -X POST "%BASE_URL%/api/mqtt/command/device/device001" ^
     -H "Content-Type: application/json" ^
     -d "{\"command\":\"{\\\"type\\\":\\\"get_status\\\"}\"}" | echo.
echo.

echo ========================================
echo API测试完成
echo ========================================
echo.
echo 按任意键退出...
pause >nul
