# 物联网传感器服务器

基于Spring Boot和EMQX的物联网传感器数据管理系统，支持设备认证、数据上传、指令下发等功能。

## 系统架构

```
┌─────────────────┐    MQTT     ┌─────────────────┐    HTTP     ┌─────────────────┐
│   IoT Devices   │ ◄─────────► │   EMQX Server   │ ◄─────────► │  Spring Boot    │
│  (传感器设备)    │             │  (MQTT Broker)  │             │   (后端服务)     │
└─────────────────┘             └─────────────────┘             └─────────────────┘
                                                                          │
                                                                          ▼
                                                                ┌─────────────────┐
                                                                │  MySQL Database │
                                                                │   (数据存储)     │
                                                                └─────────────────┘
```

## 功能特性

- ✅ 设备认证和登录管理
- ✅ 实时数据上传和存储
- ✅ 设备状态监控
- ✅ 指令下发功能
- ✅ RESTful API接口
- ✅ 设备在线/离线状态管理
- ✅ 消息历史记录查询

## 技术栈

### 后端服务
- **Spring Boot 3.5.4** - 主框架
- **Spring Integration MQTT** - MQTT集成
- **Spring Data JPA** - 数据访问层
- **MySQL 8.0** - 数据库
- **Lombok** - 代码简化

### MQTT服务器
- **EMQX 5.1.0** - MQTT消息代理
- **Docker** - 容器化部署

### 设备模拟器
- **Python 3.6+** - 开发语言
- **paho-mqtt** - MQTT客户端库

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- Java 21+
- Maven 3.6+
- Docker & Docker Compose
- MySQL 8.0
- Python 3.6+ (用于设备模拟器)

### 2. 数据库初始化

```sql
-- 连接MySQL并执行初始化脚本
mysql -u root -p987654 < init_database.sql
```

### 3. 启动服务

#### 方式一：使用启动脚本（推荐）
```bash
# Windows
start-services.bat

# 或手动启动
docker-compose up -d
mvnw spring-boot:run
```

#### 方式二：分步启动
```bash
# 1. 启动EMQX
docker-compose up -d emqx

# 2. 启动Spring Boot应用
mvnw spring-boot:run
```

### 4. 启动设备模拟器

```bash
# Windows
start-device-simulator.bat

# 或直接运行Python脚本
python device_simulator.py --device-id device001 --username device001 --password password123
```

## 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| Spring Boot API | http://localhost:8080 | 后端API服务 |
| EMQX Dashboard | http://localhost:18083 | MQTT管理界面 |
| MySQL | localhost:3306 | 数据库服务 |

### EMQX Dashboard 登录信息
- 用户名: `admin`
- 密码: `public`

## API接口文档

### 设备管理接口

#### 获取所有设备
```http
GET /api/devices
```

#### 获取在线设备
```http
GET /api/devices/online
```

#### 获取离线设备
```http
GET /api/devices/offline
```

#### 获取设备状态统计
```http
GET /api/devices/status/summary
```

### MQTT消息接口

#### 获取设备消息
```http
GET /api/mqtt/messages/device/{deviceId}
```

#### 获取最新消息
```http
GET /api/mqtt/messages/latest?limit=20
```

#### 发送指令到设备
```http
POST /api/mqtt/command/device/{deviceId}
Content-Type: application/json

{
  "command": "{\"type\":\"get_status\"}"
}
```

## MQTT主题规范

### 设备端主题

| 主题 | 方向 | 说明 |
|------|------|------|
| `device/{deviceId}/login` | 设备→服务器 | 设备登录请求 |
| `device/{deviceId}/data` | 设备→服务器 | 传感器数据上传 |
| `device/{deviceId}/status` | 设备→服务器 | 设备状态更新 |

### 服务器端主题

| 主题 | 方向 | 说明 |
|------|------|------|
| `device/{deviceId}/login/response` | 服务器→设备 | 登录响应 |
| `device/{deviceId}/command` | 服务器→设备 | 指令下发 |

## 设备与服务端交互详细逻辑

### 1. 设备登录流程

```mermaid
sequenceDiagram
    participant D as 设备
    participant E as EMQX
    participant S as Spring Boot
    participant DB as MySQL

    D->>E: 连接MQTT服务器
    E-->>D: 连接成功
    
    D->>E: 订阅响应主题
    Note over D: device/{deviceId}/login/response
    Note over D: device/{deviceId}/command
    
    D->>E: 发布登录请求
    Note over D,E: Topic: device/{deviceId}/login<br/>Payload: {"username":"xxx","password":"xxx","ip":"xxx"}
    
    E->>S: 转发登录消息
    S->>DB: 验证用户名密码
    DB-->>S: 返回验证结果
    
    alt 认证成功
        S->>DB: 更新设备在线状态
        S->>E: 发布登录成功响应
        E->>D: 转发响应消息
        Note over E,D: Topic: device/{deviceId}/login/response<br/>Payload: {"success":true,"message":"Login successful"}
        D->>E: 发布在线状态
        Note over D,E: Topic: device/{deviceId}/status<br/>Payload: {"status":"online","ip":"xxx"}
    else 认证失败
        S->>E: 发布登录失败响应
        E->>D: 转发响应消息
        Note over E,D: Payload: {"success":false,"message":"Authentication failed"}
    end
```

### 2. 数据上传流程

```mermaid
sequenceDiagram
    participant D as 设备
    participant E as EMQX
    participant S as Spring Boot
    participant DB as MySQL

    loop 每30秒
        D->>E: 发布传感器数据
        Note over D,E: Topic: device/{deviceId}/data<br/>Payload: {"type":"sensor_data","temperature":25.5,"humidity":60.2}
        
        E->>S: 转发数据消息
        S->>DB: 保存消息记录
        S->>DB: 验证设备在线状态
        
        alt 设备在线
            Note over S: 处理数据（验证、存储、转发等）
        else 设备离线
            Note over S: 记录警告日志
        end
    end
```

### 3. 指令下发流程

```mermaid
sequenceDiagram
    participant API as API客户端
    participant S as Spring Boot
    participant E as EMQX
    participant D as 设备
    participant DB as MySQL

    API->>S: POST /api/mqtt/command/device/{deviceId}
    Note over API,S: Body: {"command":"{\"type\":\"get_status\"}"}
    
    S->>DB: 保存发送消息记录
    S->>E: 发布指令消息
    Note over S,E: Topic: device/{deviceId}/command<br/>Payload: {"type":"get_status"}
    
    E->>D: 转发指令消息
    D->>D: 处理指令
    
    alt 状态查询指令
        D->>E: 发布设备信息
        Note over D,E: Topic: device/{deviceId}/data<br/>Payload: {"type":"device_info","firmware_version":"1.0.0"}
    else 重启指令
        D->>E: 发布重启状态
        Note over D,E: Topic: device/{deviceId}/status<br/>Payload: {"status":"restarting"}
        D->>D: 模拟重启
        D->>E: 发布在线状态
        Note over D,E: Payload: {"status":"online"}
    end
    
    S-->>API: 返回发送结果
```

### 4. 设备状态管理

```mermaid
stateDiagram-v2
    [*] --> 离线
    离线 --> 连接中: MQTT连接
    连接中 --> 认证中: 订阅主题完成
    认证中 --> 在线: 登录成功
    认证中 --> 离线: 登录失败
    在线 --> 离线: 断开连接/登出
    在线 --> 在线: 数据上传/指令处理
```

## 消息格式规范

### 登录请求消息
```json
{
  "username": "device001",
  "password": "password123",
  "ip": "*************",
  "timestamp": 1704067200000
}
```

### 登录响应消息
```json
{
  "success": true,
  "message": "Login successful",
  "timestamp": 1704067200000
}
```

### 传感器数据消息
```json
{
  "type": "sensor_data",
  "temperature": 25.5,
  "humidity": 60.2,
  "pressure": 1013.25,
  "timestamp": 1704067200000
}
```

### 设备状态消息
```json
{
  "status": "online",
  "ip": "*************",
  "timestamp": 1704067200000
}
```

### 指令消息
```json
{
  "type": "get_status",
  "data": {}
}
```

## 项目结构

```
mqtt_server/
├── src/main/java/cn/xyhh/mqtt_server/
│   ├── config/          # 配置类
│   ├── controller/      # REST控制器
│   ├── dto/            # 数据传输对象
│   ├── entity/         # 数据库实体
│   ├── repository/     # 数据访问层
│   └── service/        # 业务逻辑层
├── src/main/resources/
│   └── application.yml # 应用配置
├── docker-compose.yml  # Docker编排文件
├── device_simulator.py # 设备模拟器
├── init_database.sql   # 数据库初始化脚本
├── start-services.bat  # 服务启动脚本
└── README.md          # 项目说明
```

## 故障排除

### 常见问题

1. **EMQX启动失败**
   - 检查Docker是否正常运行
   - 确认端口1883、18083未被占用

2. **Spring Boot连接数据库失败**
   - 检查MySQL服务是否启动
   - 确认数据库连接信息是否正确

3. **设备连接MQTT失败**
   - 检查EMQX是否正常运行
   - 确认网络连接是否正常

4. **设备认证失败**
   - 检查数据库中是否存在对应的设备记录
   - 确认用户名密码是否正确

### 日志查看

```bash
# 查看EMQX日志
docker logs emqx-mqtt-server

# 查看Spring Boot日志
# 在启动窗口中查看控制台输出
```

## 开发说明

### 添加新的设备类型
1. 在数据库中添加设备记录
2. 修改设备模拟器支持新的传感器类型
3. 根据需要扩展消息处理逻辑

### 扩展API接口
1. 在对应的Controller中添加新的接口方法
2. 在Service层实现业务逻辑
3. 更新API文档

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
