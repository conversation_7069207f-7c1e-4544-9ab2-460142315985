package cn.xyhh.mqtt_server.repository;

import cn.xyhh.mqtt_server.entity.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 设备数据访问接口
 */
@Repository
public interface DeviceRepository extends JpaRepository<Device, Long> {
    
    /**
     * 根据设备ID查找设备
     */
    Optional<Device> findByDeviceId(String deviceId);
    
    /**
     * 根据用户名查找设备
     */
    Optional<Device> findByUsername(String username);
    
    /**
     * 根据用户名和密码查找设备
     */
    Optional<Device> findByUsernameAndPassword(String username, String password);
    
    /**
     * 查找所有在线设备
     */
    List<Device> findByOnlineTrue();
    
    /**
     * 查找所有离线设备
     */
    List<Device> findByOnlineFalse();
    
    /**
     * 更新设备在线状态
     */
    @Modifying
    @Query("UPDATE Device d SET d.online = :online, d.updatedAt = :updatedAt WHERE d.deviceId = :deviceId")
    int updateOnlineStatus(@Param("deviceId") String deviceId, 
                          @Param("online") Boolean online, 
                          @Param("updatedAt") LocalDateTime updatedAt);
    
    /**
     * 更新设备连接信息
     */
    @Modifying
    @Query("UPDATE Device d SET d.online = :online, d.lastIpAddress = :ipAddress, " +
           "d.lastConnectedAt = :connectedAt, d.updatedAt = :updatedAt WHERE d.deviceId = :deviceId")
    int updateConnectionInfo(@Param("deviceId") String deviceId,
                           @Param("online") Boolean online,
                           @Param("ipAddress") String ipAddress,
                           @Param("connectedAt") LocalDateTime connectedAt,
                           @Param("updatedAt") LocalDateTime updatedAt);
    
    /**
     * 更新设备断开连接信息
     */
    @Modifying
    @Query("UPDATE Device d SET d.online = false, d.lastDisconnectedAt = :disconnectedAt, " +
           "d.updatedAt = :updatedAt WHERE d.deviceId = :deviceId")
    int updateDisconnectionInfo(@Param("deviceId") String deviceId,
                               @Param("disconnectedAt") LocalDateTime disconnectedAt,
                               @Param("updatedAt") LocalDateTime updatedAt);
}
