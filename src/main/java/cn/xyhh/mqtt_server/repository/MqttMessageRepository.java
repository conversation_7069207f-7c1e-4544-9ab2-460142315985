package cn.xyhh.mqtt_server.repository;

import cn.xyhh.mqtt_server.entity.MqttMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MQTT消息数据访问接口
 */
@Repository
public interface MqttMessageRepository extends JpaRepository<MqttMessage, Long> {
    
    /**
     * 根据设备ID查找消息
     */
    List<MqttMessage> findByDeviceId(String deviceId);
    
    /**
     * 根据设备ID分页查找消息
     */
    Page<MqttMessage> findByDeviceId(String deviceId, Pageable pageable);
    
    /**
     * 根据消息类型查找消息
     */
    List<MqttMessage> findByMessageType(MqttMessage.MessageType messageType);
    
    /**
     * 根据设备ID和消息类型查找消息
     */
    List<MqttMessage> findByDeviceIdAndMessageType(String deviceId, MqttMessage.MessageType messageType);
    
    /**
     * 根据时间范围查找消息
     */
    @Query("SELECT m FROM MqttMessage m WHERE m.createdAt BETWEEN :startTime AND :endTime ORDER BY m.createdAt DESC")
    List<MqttMessage> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据设备ID和时间范围查找消息
     */
    @Query("SELECT m FROM MqttMessage m WHERE m.deviceId = :deviceId AND m.createdAt BETWEEN :startTime AND :endTime ORDER BY m.createdAt DESC")
    List<MqttMessage> findByDeviceIdAndTimeRange(@Param("deviceId") String deviceId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找最新的N条消息
     */
    @Query("SELECT m FROM MqttMessage m ORDER BY m.createdAt DESC")
    List<MqttMessage> findLatestMessages(Pageable pageable);
    
    /**
     * 根据设备ID查找最新的N条消息
     */
    @Query("SELECT m FROM MqttMessage m WHERE m.deviceId = :deviceId ORDER BY m.createdAt DESC")
    List<MqttMessage> findLatestMessagesByDeviceId(@Param("deviceId") String deviceId, Pageable pageable);
}
