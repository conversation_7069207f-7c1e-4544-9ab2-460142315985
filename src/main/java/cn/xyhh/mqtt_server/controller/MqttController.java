package cn.xyhh.mqtt_server.controller;

import cn.xyhh.mqtt_server.dto.ApiResponse;
import cn.xyhh.mqtt_server.dto.MqttMessageDTO;
import cn.xyhh.mqtt_server.service.MqttMessageService;
import cn.xyhh.mqtt_server.service.MqttService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MQTT消息管理控制器
 */
@RestController
@RequestMapping("/api/mqtt")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class MqttController {
    
    private final MqttMessageService mqttMessageService;
    private final MqttService mqttService;
    
    /**
     * 获取设备的消息列表
     */
    @GetMapping("/messages/device/{deviceId}")
    public ApiResponse<List<MqttMessageDTO>> getMessagesByDeviceId(@PathVariable String deviceId) {
        try {
            List<MqttMessageDTO> messages = mqttMessageService.getMessagesByDeviceId(deviceId);
            return ApiResponse.success("获取设备消息列表成功", messages);
        } catch (Exception e) {
            log.error("获取设备消息列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取设备消息列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取设备的最新消息
     */
    @GetMapping("/messages/device/{deviceId}/latest")
    public ApiResponse<List<MqttMessageDTO>> getLatestMessagesByDeviceId(
            @PathVariable String deviceId,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<MqttMessageDTO> messages = mqttMessageService.getLatestMessagesByDeviceId(deviceId, limit);
            return ApiResponse.success("获取设备最新消息成功", messages);
        } catch (Exception e) {
            log.error("获取设备最新消息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取设备最新消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取最新消息列表
     */
    @GetMapping("/messages/latest")
    public ApiResponse<List<MqttMessageDTO>> getLatestMessages(@RequestParam(defaultValue = "20") int limit) {
        try {
            List<MqttMessageDTO> messages = mqttMessageService.getLatestMessages(limit);
            return ApiResponse.success("获取最新消息列表成功", messages);
        } catch (Exception e) {
            log.error("获取最新消息列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取最新消息列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据时间范围获取消息
     */
    @GetMapping("/messages/range")
    public ApiResponse<List<MqttMessageDTO>> getMessagesByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            List<MqttMessageDTO> messages = mqttMessageService.getMessagesByTimeRange(startTime, endTime);
            return ApiResponse.success("获取时间范围内消息成功", messages);
        } catch (Exception e) {
            log.error("获取时间范围内消息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取时间范围内消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据设备ID和时间范围获取消息
     */
    @GetMapping("/messages/device/{deviceId}/range")
    public ApiResponse<List<MqttMessageDTO>> getMessagesByDeviceIdAndTimeRange(
            @PathVariable String deviceId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            List<MqttMessageDTO> messages = mqttMessageService.getMessagesByDeviceIdAndTimeRange(deviceId, startTime, endTime);
            return ApiResponse.success("获取设备时间范围内消息成功", messages);
        } catch (Exception e) {
            log.error("获取设备时间范围内消息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取设备时间范围内消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 向设备发送指令
     */
    @PostMapping("/command/device/{deviceId}")
    public ApiResponse<String> sendCommandToDevice(
            @PathVariable String deviceId,
            @RequestBody CommandRequest request) {
        try {
            mqttService.sendCommandToDevice(deviceId, request.getCommand());
            return ApiResponse.success("指令发送成功", "Command sent to device: " + deviceId);
        } catch (Exception e) {
            log.error("发送指令失败: {}", e.getMessage(), e);
            return ApiResponse.error("发送指令失败: " + e.getMessage());
        }
    }
    
    /**
     * 指令请求DTO
     */
    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CommandRequest {
        private String command;
    }
}
