package cn.xyhh.mqtt_server.controller;

import cn.xyhh.mqtt_server.dto.ApiResponse;
import cn.xyhh.mqtt_server.dto.DeviceDTO;
import cn.xyhh.mqtt_server.service.DeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 设备管理控制器
 */
@RestController
@RequestMapping("/api/devices")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class DeviceController {
    
    private final DeviceService deviceService;
    
    /**
     * 获取所有设备列表
     */
    @GetMapping
    public ApiResponse<List<DeviceDTO>> getAllDevices() {
        try {
            List<DeviceDTO> devices = deviceService.getAllDevices();
            return ApiResponse.success("获取设备列表成功", devices);
        } catch (Exception e) {
            log.error("获取设备列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取设备列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据设备ID获取设备信息
     */
    @GetMapping("/{deviceId}")
    public ApiResponse<DeviceDTO> getDeviceById(@PathVariable String deviceId) {
        try {
            Optional<DeviceDTO> device = deviceService.getDeviceById(deviceId);
            if (device.isPresent()) {
                return ApiResponse.success("获取设备信息成功", device.get());
            } else {
                return ApiResponse.error(404, "设备不存在");
            }
        } catch (Exception e) {
            log.error("获取设备信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取设备信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取在线设备列表
     */
    @GetMapping("/online")
    public ApiResponse<List<DeviceDTO>> getOnlineDevices() {
        try {
            List<DeviceDTO> devices = deviceService.getOnlineDevices();
            return ApiResponse.success("获取在线设备列表成功", devices);
        } catch (Exception e) {
            log.error("获取在线设备列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取在线设备列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取离线设备列表
     */
    @GetMapping("/offline")
    public ApiResponse<List<DeviceDTO>> getOfflineDevices() {
        try {
            List<DeviceDTO> devices = deviceService.getOfflineDevices();
            return ApiResponse.success("获取离线设备列表成功", devices);
        } catch (Exception e) {
            log.error("获取离线设备列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取离线设备列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取设备状态统计
     */
    @GetMapping("/status/summary")
    public ApiResponse<DeviceStatusSummary> getDeviceStatusSummary() {
        try {
            List<DeviceDTO> allDevices = deviceService.getAllDevices();
            List<DeviceDTO> onlineDevices = deviceService.getOnlineDevices();
            List<DeviceDTO> offlineDevices = deviceService.getOfflineDevices();
            
            DeviceStatusSummary summary = DeviceStatusSummary.builder()
                    .totalDevices(allDevices.size())
                    .onlineDevices(onlineDevices.size())
                    .offlineDevices(offlineDevices.size())
                    .build();
            
            return ApiResponse.success("获取设备状态统计成功", summary);
        } catch (Exception e) {
            log.error("获取设备状态统计失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取设备状态统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 设备状态统计DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class DeviceStatusSummary {
        private Integer totalDevices;
        private Integer onlineDevices;
        private Integer offlineDevices;
    }
}
