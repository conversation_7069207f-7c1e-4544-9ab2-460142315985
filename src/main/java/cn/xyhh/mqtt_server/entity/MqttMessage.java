package cn.xyhh.mqtt_server.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * MQTT消息实体类
 */
@Entity
@Table(name = "mqtt_messages")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MqttMessage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "device_id", nullable = false)
    private String deviceId;
    
    @Column(name = "topic", nullable = false)
    private String topic;
    
    @Column(name = "payload", columnDefinition = "TEXT")
    private String payload;
    
    @Column(name = "qos")
    private Integer qos = 0;
    
    @Column(name = "retained", columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean retained = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "message_type", nullable = false)
    private MessageType messageType;
    
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        INCOMING,  // 接收的消息
        OUTGOING   // 发送的消息
    }
}
