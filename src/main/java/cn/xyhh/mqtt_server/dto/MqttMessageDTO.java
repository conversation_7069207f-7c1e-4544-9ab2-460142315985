package cn.xyhh.mqtt_server.dto;

import cn.xyhh.mqtt_server.entity.MqttMessage;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * MQTT消息数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MqttMessageDTO {
    
    private Long id;
    private String deviceId;
    private String topic;
    private String payload;
    private Integer qos;
    private Boolean retained;
    private MqttMessage.MessageType messageType;
    private LocalDateTime createdAt;
}
