package cn.xyhh.mqtt_server.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 设备数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDTO {
    
    private Long id;
    private String deviceId;
    private String deviceName;
    private String username;
    private Boolean online;
    private String lastIpAddress;
    private LocalDateTime lastConnectedAt;
    private LocalDateTime lastDisconnectedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
