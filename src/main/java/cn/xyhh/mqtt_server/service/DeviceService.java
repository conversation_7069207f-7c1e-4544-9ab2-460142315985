package cn.xyhh.mqtt_server.service;

import cn.xyhh.mqtt_server.dto.DeviceDTO;
import cn.xyhh.mqtt_server.entity.Device;
import cn.xyhh.mqtt_server.repository.DeviceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceService {
    
    private final DeviceRepository deviceRepository;
    
    /**
     * 获取所有设备
     */
    public List<DeviceDTO> getAllDevices() {
        return deviceRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据设备ID获取设备
     */
    public Optional<DeviceDTO> getDeviceById(String deviceId) {
        return deviceRepository.findByDeviceId(deviceId)
                .map(this::convertToDTO);
    }
    
    /**
     * 获取在线设备列表
     */
    public List<DeviceDTO> getOnlineDevices() {
        return deviceRepository.findByOnlineTrue().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取离线设备列表
     */
    public List<DeviceDTO> getOfflineDevices() {
        return deviceRepository.findByOnlineFalse().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 设备认证
     */
    public boolean authenticateDevice(String username, String password) {
        Optional<Device> device = deviceRepository.findByUsernameAndPassword(username, password);
        if (device.isPresent()) {
            log.info("Device authentication successful for username: {}", username);
            return true;
        } else {
            log.warn("Device authentication failed for username: {}", username);
            return false;
        }
    }
    
    /**
     * 设备上线
     */
    @Transactional
    public void deviceOnline(String deviceId, String ipAddress) {
        LocalDateTime now = LocalDateTime.now();
        int updated = deviceRepository.updateConnectionInfo(deviceId, true, ipAddress, now, now);
        if (updated > 0) {
            log.info("Device {} is now online from IP: {}", deviceId, ipAddress);
        } else {
            log.warn("Failed to update online status for device: {}", deviceId);
        }
    }
    
    /**
     * 设备下线
     */
    @Transactional
    public void deviceOffline(String deviceId) {
        LocalDateTime now = LocalDateTime.now();
        int updated = deviceRepository.updateDisconnectionInfo(deviceId, now, now);
        if (updated > 0) {
            log.info("Device {} is now offline", deviceId);
        } else {
            log.warn("Failed to update offline status for device: {}", deviceId);
        }
    }
    
    /**
     * 创建新设备
     */
    @Transactional
    public DeviceDTO createDevice(Device device) {
        Device savedDevice = deviceRepository.save(device);
        log.info("Created new device: {}", savedDevice.getDeviceId());
        return convertToDTO(savedDevice);
    }
    
    /**
     * 根据用户名获取设备
     */
    public Optional<DeviceDTO> getDeviceByUsername(String username) {
        return deviceRepository.findByUsername(username)
                .map(this::convertToDTO);
    }
    
    /**
     * 转换为DTO
     */
    private DeviceDTO convertToDTO(Device device) {
        return DeviceDTO.builder()
                .id(device.getId())
                .deviceId(device.getDeviceId())
                .deviceName(device.getDeviceName())
                .username(device.getUsername())
                .online(device.getOnline())
                .lastIpAddress(device.getLastIpAddress())
                .lastConnectedAt(device.getLastConnectedAt())
                .lastDisconnectedAt(device.getLastDisconnectedAt())
                .createdAt(device.getCreatedAt())
                .updatedAt(device.getUpdatedAt())
                .build();
    }
}
