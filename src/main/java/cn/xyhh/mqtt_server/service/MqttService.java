package cn.xyhh.mqtt_server.service;

import cn.xyhh.mqtt_server.dto.DeviceDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * MQTT服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MqttService {
    
    private final DeviceService deviceService;
    private final MqttMessageService mqttMessageService;
    private final MessageChannel mqttOutputChannel;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Value("${mqtt.server.topics.device-login}")
    private String deviceLoginTopic;
    
    @Value("${mqtt.server.topics.device-data}")
    private String deviceDataTopic;
    
    @Value("${mqtt.server.topics.device-command}")
    private String deviceCommandTopic;
    
    @Value("${mqtt.server.topics.device-status}")
    private String deviceStatusTopic;
    
    /**
     * 处理接收到的MQTT消息
     */
    public void handleIncomingMessage(Message<?> message) {
        try {
            String topic = (String) message.getHeaders().get("mqtt_receivedTopic");
            String payload = message.getPayload().toString();
            Integer qos = (Integer) message.getHeaders().get("mqtt_receivedQos");
            Boolean retained = (Boolean) message.getHeaders().get("mqtt_receivedRetained");
            
            log.info("Received MQTT message - Topic: {}, Payload: {}", topic, payload);
            
            // 从topic中提取设备ID
            String deviceId = extractDeviceIdFromTopic(topic);
            if (deviceId == null) {
                log.warn("Cannot extract device ID from topic: {}", topic);
                return;
            }
            
            // 保存消息到数据库
            mqttMessageService.saveIncomingMessage(deviceId, topic, payload, qos, retained);
            
            // 根据topic类型处理消息
            if (topic.contains("/login")) {
                handleDeviceLogin(deviceId, payload);
            } else if (topic.contains("/data")) {
                handleDeviceData(deviceId, payload);
            } else if (topic.contains("/status")) {
                handleDeviceStatus(deviceId, payload);
            }
            
        } catch (Exception e) {
            log.error("Error processing MQTT message: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理设备登录
     */
    private void handleDeviceLogin(String deviceId, String payload) {
        try {
            JsonNode loginData = objectMapper.readTree(payload);
            String username = loginData.get("username").asText();
            String password = loginData.get("password").asText();
            String ipAddress = loginData.has("ip") ? loginData.get("ip").asText() : "unknown";
            
            // 验证设备认证
            if (deviceService.authenticateDevice(username, password)) {
                // 设备上线
                deviceService.deviceOnline(deviceId, ipAddress);
                
                // 发送登录成功响应
                sendLoginResponse(deviceId, true, "Login successful");
                log.info("Device {} logged in successfully", deviceId);
            } else {
                // 发送登录失败响应
                sendLoginResponse(deviceId, false, "Authentication failed");
                log.warn("Device {} login failed", deviceId);
            }
            
        } catch (Exception e) {
            log.error("Error handling device login for {}: {}", deviceId, e.getMessage(), e);
            sendLoginResponse(deviceId, false, "Login error: " + e.getMessage());
        }
    }
    
    /**
     * 处理设备数据
     */
    private void handleDeviceData(String deviceId, String payload) {
        try {
            // 验证设备是否在线
            Optional<DeviceDTO> device = deviceService.getDeviceById(deviceId);
            if (device.isPresent() && device.get().getOnline()) {
                log.info("Received data from device {}: {}", deviceId, payload);
                // 这里可以添加数据处理逻辑，比如数据验证、存储、转发等
            } else {
                log.warn("Received data from offline or unknown device: {}", deviceId);
            }
        } catch (Exception e) {
            log.error("Error handling device data for {}: {}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 处理设备状态
     */
    private void handleDeviceStatus(String deviceId, String payload) {
        try {
            JsonNode statusData = objectMapper.readTree(payload);
            String status = statusData.get("status").asText();
            
            if ("online".equals(status)) {
                String ipAddress = statusData.has("ip") ? statusData.get("ip").asText() : "unknown";
                deviceService.deviceOnline(deviceId, ipAddress);
            } else if ("offline".equals(status)) {
                deviceService.deviceOffline(deviceId);
            }
            
            log.info("Device {} status updated: {}", deviceId, status);
        } catch (Exception e) {
            log.error("Error handling device status for {}: {}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 发送登录响应
     */
    private void sendLoginResponse(String deviceId, boolean success, String message) {
        try {
            String responseTopic = String.format("device/%s/login/response", deviceId);
            String responsePayload = String.format("{\"success\":%b,\"message\":\"%s\",\"timestamp\":%d}", 
                    success, message, System.currentTimeMillis());
            
            sendMessage(responseTopic, responsePayload);
        } catch (Exception e) {
            log.error("Error sending login response to device {}: {}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 发送指令到设备
     */
    public void sendCommandToDevice(String deviceId, String command) {
        try {
            String commandTopic = String.format("device/%s/command", deviceId);
            sendMessage(commandTopic, command);
            
            // 保存发送的消息
            mqttMessageService.saveOutgoingMessage(deviceId, commandTopic, command, 1, false);
            log.info("Sent command to device {}: {}", deviceId, command);
        } catch (Exception e) {
            log.error("Error sending command to device {}: {}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 发送MQTT消息
     */
    private void sendMessage(String topic, String payload) {
        Message<String> message = MessageBuilder.withPayload(payload)
                .setHeader("mqtt_topic", topic)
                .setHeader("mqtt_qos", 1)
                .setHeader("mqtt_retained", false)
                .build();
        
        mqttOutputChannel.send(message);
    }
    
    /**
     * 从topic中提取设备ID
     */
    private String extractDeviceIdFromTopic(String topic) {
        try {
            // topic格式: device/{deviceId}/xxx
            String[] parts = topic.split("/");
            if (parts.length >= 3 && "device".equals(parts[0])) {
                return parts[1];
            }
        } catch (Exception e) {
            log.error("Error extracting device ID from topic {}: {}", topic, e.getMessage());
        }
        return null;
    }
}
