package cn.xyhh.mqtt_server.service;

import cn.xyhh.mqtt_server.dto.MqttMessageDTO;
import cn.xyhh.mqtt_server.entity.MqttMessage;
import cn.xyhh.mqtt_server.repository.MqttMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MQTT消息服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MqttMessageService {
    
    private final MqttMessageRepository mqttMessageRepository;
    
    /**
     * 保存接收到的消息
     */
    @Transactional
    public MqttMessageDTO saveIncomingMessage(String deviceId, String topic, String payload, Integer qos, <PERSON><PERSON><PERSON> retained) {
        MqttMessage message = MqttMessage.builder()
                .deviceId(deviceId)
                .topic(topic)
                .payload(payload)
                .qos(qos)
                .retained(retained)
                .messageType(MqttMessage.MessageType.INCOMING)
                .build();
        
        MqttMessage savedMessage = mqttMessageRepository.save(message);
        log.debug("Saved incoming message from device {}: {}", deviceId, topic);
        return convertToDTO(savedMessage);
    }
    
    /**
     * 保存发送的消息
     */
    @Transactional
    public MqttMessageDTO saveOutgoingMessage(String deviceId, String topic, String payload, Integer qos, Boolean retained) {
        MqttMessage message = MqttMessage.builder()
                .deviceId(deviceId)
                .topic(topic)
                .payload(payload)
                .qos(qos)
                .retained(retained)
                .messageType(MqttMessage.MessageType.OUTGOING)
                .build();
        
        MqttMessage savedMessage = mqttMessageRepository.save(message);
        log.debug("Saved outgoing message to device {}: {}", deviceId, topic);
        return convertToDTO(savedMessage);
    }
    
    /**
     * 根据设备ID获取消息列表
     */
    public List<MqttMessageDTO> getMessagesByDeviceId(String deviceId) {
        return mqttMessageRepository.findByDeviceId(deviceId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据设备ID获取最新的N条消息
     */
    public List<MqttMessageDTO> getLatestMessagesByDeviceId(String deviceId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return mqttMessageRepository.findLatestMessagesByDeviceId(deviceId, pageable).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最新的N条消息
     */
    public List<MqttMessageDTO> getLatestMessages(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return mqttMessageRepository.findLatestMessages(pageable).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据时间范围获取消息
     */
    public List<MqttMessageDTO> getMessagesByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return mqttMessageRepository.findByTimeRange(startTime, endTime).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据设备ID和时间范围获取消息
     */
    public List<MqttMessageDTO> getMessagesByDeviceIdAndTimeRange(String deviceId, LocalDateTime startTime, LocalDateTime endTime) {
        return mqttMessageRepository.findByDeviceIdAndTimeRange(deviceId, startTime, endTime).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据消息类型获取消息
     */
    public List<MqttMessageDTO> getMessagesByType(MqttMessage.MessageType messageType) {
        return mqttMessageRepository.findByMessageType(messageType).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 转换为DTO
     */
    private MqttMessageDTO convertToDTO(MqttMessage message) {
        return MqttMessageDTO.builder()
                .id(message.getId())
                .deviceId(message.getDeviceId())
                .topic(message.getTopic())
                .payload(message.getPayload())
                .qos(message.getQos())
                .retained(message.getRetained())
                .messageType(message.getMessageType())
                .createdAt(message.getCreatedAt())
                .build();
    }
}
