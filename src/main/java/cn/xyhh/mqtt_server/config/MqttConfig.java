package cn.xyhh.mqtt_server.config;

import cn.xyhh.mqtt_server.service.MqttService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

/**
 * MQTT配置类
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class MqttConfig {
    
    @Value("${spring.integration.mqtt.default.url}")
    private String mqttUrl;
    
    @Value("${spring.integration.mqtt.default.username}")
    private String mqttUsername;
    
    @Value("${spring.integration.mqtt.default.password}")
    private String mqttPassword;
    
    @Value("${spring.integration.mqtt.default.client-id}")
    private String clientId;
    
    @Value("${mqtt.server.topics.device-login}")
    private String deviceLoginTopic;
    
    @Value("${mqtt.server.topics.device-data}")
    private String deviceDataTopic;
    
    @Value("${mqtt.server.topics.device-command}")
    private String deviceCommandTopic;
    
    @Value("${mqtt.server.topics.device-status}")
    private String deviceStatusTopic;
    
    @Value("${mqtt.server.qos}")
    private int qos;
    
    private final MqttService mqttService;
    
    /**
     * MQTT客户端工厂
     */
    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{mqttUrl});
        options.setUserName(mqttUsername);
        options.setPassword(mqttPassword.toCharArray());
        options.setCleanSession(true);
        options.setConnectionTimeout(30);
        options.setKeepAliveInterval(60);
        factory.setConnectionOptions(options);
        return factory;
    }
    
    /**
     * MQTT输入通道
     */
    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }
    
    /**
     * MQTT输出通道
     */
    @Bean
    public MessageChannel mqttOutputChannel() {
        return new DirectChannel();
    }
    
    /**
     * MQTT消息接收适配器
     */
    @Bean
    public MessageProducer inbound() {
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(
                clientId + "-inbound", mqttClientFactory(),
                deviceLoginTopic, deviceDataTopic, deviceStatusTopic);
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(qos);
        adapter.setOutputChannel(mqttInputChannel());
        return adapter;
    }
    
    /**
     * MQTT消息处理器
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler mqttMessageHandler() {
        return message -> {
            try {
                mqttService.handleIncomingMessage(message);
            } catch (Exception e) {
                log.error("Error handling MQTT message: {}", e.getMessage(), e);
            }
        };
    }
    
    /**
     * MQTT消息发送处理器
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttOutputChannel")
    public MessageHandler mqttOutbound() {
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(
                clientId + "-outbound", mqttClientFactory());
        messageHandler.setAsync(true);
        messageHandler.setDefaultTopic(deviceCommandTopic);
        messageHandler.setDefaultQos(qos);
        return messageHandler;
    }
}
