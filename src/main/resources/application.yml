server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      force: true

spring:
  datasource:
    url: ************************************************************************************************************************
    username: root
    password: 987654
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
    
  integration:
    mqtt:
      default:
        url: tcp://localhost:1883
        client-id: mqtt-server-${random.value}
        username: admin
        password: public
        clean-session: true
        connection-timeout: 30
        keep-alive-interval: 60
        completion-timeout: 5000

logging:
  level:
    cn.xyhh.mqtt_server: DEBUG
    org.springframework.integration: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

mqtt:
  server:
    topics:
      device-login: "device/+/login"
      device-data: "device/+/data"
      device-command: "device/+/command"
      device-status: "device/+/status"
    qos: 1
