# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Spring Boot 3.5.4 application named `mqtt_server` built with Java 21. The project uses <PERSON><PERSON> as the build tool and includes Spring Boot starter dependencies with Lombok for boilerplate reduction.

## Build and Development Commands

### Maven Commands
- **Build**: `./mvnw clean package` (or `mvnw.cmd clean package` on Windows)
- **Run**: `./mvnw spring-boot:run` (or `mvnw.cmd spring-boot:run` on Windows)
- **Test**: `./mvnw test` (or `mvnw.cmd test` on Windows)
- **Single test**: `./mvnw test -Dtest=MqttServerApplicationTests` (or `mvnw.cmd test -Dtest=MqttServerApplicationTests` on Windows)

### Java Version
- Java 21 is required for this project

## Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── cn/xyhh/mqtt_server/
│   │       └── MqttServerApplication.java
│   └── resources/
│       └── application.properties
└── test/
    └── java/
        └── cn/xyhh/mqtt_server/
            └── MqttServerApplicationTests.java
```

## Key Configuration

- **Application name**: mqtt_server (configured in application.properties)
- **Main class**: `cn.xyhh.mqtt_server.MqttServerApplication`
- **Dependencies**: Spring Boot starter, Lombok (optional), Spring Boot test

## Current State

The project is in its initial setup phase with a basic Spring Boot application structure. The main application class exists but no MQTT-specific functionality has been implemented yet.