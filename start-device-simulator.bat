@echo off
chcp 65001 >nul
echo ========================================
echo 物联网设备模拟器启动脚本
echo ========================================

echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Python未安装，请先安装Python 3.6+
    pause
    exit /b 1
)

echo Python环境已就绪
echo.

echo 正在安装Python依赖...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo 警告: 依赖安装可能失败，但继续尝试运行
)

echo.
echo 可用的设备模拟器选项:
echo 1. 启动设备001 (温湿度传感器)
echo 2. 启动设备002 (压力传感器)
echo 3. 启动设备003 (光照传感器)
echo 4. 自定义设备
echo.

set /p choice=请选择要启动的设备 (1-4): 

if "%choice%"=="1" (
    echo 启动设备001...
    python device_simulator.py --device-id device001 --device-name "温湿度传感器01" --username device001 --password password123
) else if "%choice%"=="2" (
    echo 启动设备002...
    python device_simulator.py --device-id device002 --device-name "压力传感器01" --username device002 --password password456
) else if "%choice%"=="3" (
    echo 启动设备003...
    python device_simulator.py --device-id device003 --device-name "光照传感器01" --username device003 --password password789
) else if "%choice%"=="4" (
    set /p device_id=请输入设备ID: 
    set /p device_name=请输入设备名称: 
    set /p username=请输入用户名: 
    set /p password=请输入密码: 
    echo 启动自定义设备...
    python device_simulator.py --device-id %device_id% --device-name "%device_name%" --username %username% --password %password%
) else (
    echo 无效选择，启动默认设备...
    python device_simulator.py
)

echo.
echo 按任意键退出...
pause >nul
