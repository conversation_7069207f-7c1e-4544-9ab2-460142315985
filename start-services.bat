@echo off
chcp 65001 >nul
echo ========================================
echo 物联网传感器服务器启动脚本
echo ========================================

echo.
echo 正在检查Docker是否运行...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未运行，请先安装并启动Docker
    pause
    exit /b 1
)

echo Docker已就绪
echo.

echo 正在启动EMQX MQTT服务器...
docker-compose up -d emqx

if %errorlevel% neq 0 (
    echo 错误: EMQX启动失败
    pause
    exit /b 1
)

echo EMQX MQTT服务器启动成功
echo.

echo 等待EMQX完全启动...
timeout /t 10 /nobreak >nul

echo 正在检查EMQX状态...
docker ps | findstr emqx-mqtt-server >nul
if %errorlevel% neq 0 (
    echo 警告: EMQX容器可能未正常运行
)

echo.
echo 正在编译Spring Boot应用...
call mvnw.cmd clean compile

if %errorlevel% neq 0 (
    echo 错误: Spring Boot应用编译失败
    pause
    exit /b 1
)

echo.
echo 正在启动Spring Boot应用...
start "MQTT Server Backend" cmd /k "call mvnw.cmd spring-boot:run"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo EMQX Dashboard: http://localhost:18083
echo 用户名: admin
echo 密码: public
echo.
echo Spring Boot应用: http://localhost:8080
echo API文档: http://localhost:8080/api/devices
echo.
echo 按任意键退出...
pause >nul
